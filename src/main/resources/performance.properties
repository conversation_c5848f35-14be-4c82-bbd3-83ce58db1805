# YOP网关性能测试基础配置文件
# 
# 此文件包含所有性能测试的公共配置参数
# 特定测试类型的配置可以在专门的配置文件中覆盖这些设置
#
# <AUTHOR>
# @since 2025/1/1

# ==================== 目标服务配置 ====================

# 默认目标服务器配置
target.host=localhost
target.port=8064
target.protocol=http
target.basePath=/yop-center

# 环境特定配置
# 本地环境
local.target.host=localhost
local.target.port=8064
local.target.protocol=http

# 测试环境
test.target.host=yop-gateway-test.yeepay.com
test.target.port=80
test.target.protocol=http

# 性能测试环境
perf.target.host=yop-gateway-perf.yeepay.com
perf.target.port=80
perf.target.protocol=http

# ==================== 核心性能目标 ====================

# QPS目标设置
target.qps=2000
min.acceptable.qps=1900
peak.qps=3000

# 延迟目标设置 (毫秒)
target.tp99.ms=20
target.tp95.ms=15
target.tp90.ms=10
target.avg.ms=5

# 成功率目标 (百分比)
target.success.rate=99.5
min.success.rate=99.0

# 可用性目标 (百分比)
target.availability=99.9

# ==================== 测试执行配置 ====================

# 测试时长设置 (秒)
test.duration.seconds=300
warmup.duration.seconds=60
ramp.duration.seconds=120

# 并发用户配置
max.concurrent.users=1000
max.connections=2000

# 超时配置 (毫秒)
connection.timeout.ms=5000
request.timeout.ms=10000

# ==================== HTTP配置 ====================

# 连接池配置
http.connectionPoolSize=200
http.keepAliveTimeout=30000
http.enableHttp2=false
http.validateSsl=false

# ==================== 测试工具配置 ====================

# Gatling配置
gatling.resultsDirectory=target/gatling-results
gatling.enableReports=true
gatling.enableRealTimeMonitoring=false
gatling.jvm.opts=-Xms2g -Xmx4g -XX:+UseG1GC

# JMeter配置
jmeter.resultsDirectory=target/jmeter-results
jmeter.enableReports=true
jmeter.jvm.opts=-Xms1g -Xmx2g

# JMH配置
jmh.warmup.iterations=5
jmh.measurement.iterations=10
jmh.forks=1
jmh.threads=1

# ==================== 测试数据配置 ====================

# 测试用户数据
test.users.count=100
test.users.data.file=src/test/resources/testdata/users.csv
test.users.generate.on.missing=true

# 测试数据变化
test.data.useRandomData=true
test.data.randomSeed=12345

# 请求数据配置
request.data.variation.enabled=true
request.small.payload.percent=70
request.medium.payload.percent=25
request.large.payload.percent=5

# ==================== 监控配置 ====================

# 基础监控设置
monitoring.enabled=true
monitoring.sampleInterval=5
monitoring.metrics=cpu,memory,network,disk

# 系统监控
monitor.cpu.enabled=true
monitor.memory.enabled=true
monitor.network.enabled=true
monitor.disk.enabled=true
monitor.gc.enabled=true

# JMX监控
monitoring.jmx.port=9999

# ==================== 报告配置 ====================

# 报告生成设置
results.directory=target/performance-results
results.generateHtmlReport=true
results.generateCsvReport=true
results.generateJsonReport=true

# 报告内容设置
report.include.charts=true
report.include.system.metrics=true
report.include.jvm.metrics=true
report.include.network.metrics=true
report.include.error.analysis=true

# ==================== 日志配置 ====================

# 日志级别
logging.level=INFO
logging.file=target/logs/performance-test.log
logging.verbose=false
logging.logRequestDetails=false

# 调试配置
debug.enabled=false
debug.port=5005
debug.suspend=false
debug.logDirectory=target/debug-logs

# ==================== 错误处理配置 ====================

# 错误容忍度
error.tolerance.connection.failures=1.0
error.tolerance.timeout.failures=2.0
error.tolerance.server.errors=0.5

# 重试配置
retry.enabled=true
retry.maxAttempts=3
retry.interval=60
retry.cleanupBeforeRetry=true

# ==================== 告警配置 ====================

# 告警设置
alert.enabled=false
alert.latency.threshold=25
alert.successRate.threshold=99.0
alert.errorRate.threshold=1.0

# ==================== 环境配置 ====================

# 测试环境标识
environment=local
environment.isProduction=false

# JVM配置
jvm.heapSize.min=1g
jvm.heapSize.max=2g
jvm.gc.algorithm=G1GC
jvm.systemProperties=-Dfile.encoding=UTF-8 -Djava.net.preferIPv4Stack=true
