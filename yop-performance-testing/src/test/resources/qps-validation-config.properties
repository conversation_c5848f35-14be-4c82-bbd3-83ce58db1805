# QPS 2000目标验证测试配置文件
#
# 此配置文件定义了QPS验证测试的特有参数
# 继承基础配置：performance.properties
#
# <AUTHOR>
# @since 2025/1/1

# ==================== QPS验证特有配置 ====================

# QPS验证特定目标设置
peak.qps=2400
peak.test.duration.seconds=60
recovery.test.duration.seconds=120

# QPS验证特定用户配置
ramp.users.step=50
user.think.time.ms=1000

# ==================== 环境特定QPS缩放配置 ====================

# 环境QPS缩放因子
local.qps.scale=0.5
dev.qps.scale=0.8
test.qps.scale=1.0
staging.qps.scale=1.0
prod.qps.scale=1.0

# 环境特定服务器配置（如果与基础配置不同）
dev.target.host=yop-gateway-dev.yeepay.com
dev.target.port=80
dev.target.protocol=http

staging.target.host=yop-gateway-staging.yeepay.com
staging.target.port=443
staging.target.protocol=https

prod.target.host=yop-gateway.yeepay.com
prod.target.port=443
prod.target.protocol=https

# ==================== QPS验证流程配置 ====================

# QPS验证流程开关
enable.environment.check=true
enable.system.warmup=true
enable.baseline.test=true
enable.qps.target.test=true
enable.peak.load.test=true
enable.recovery.test=true
enable.multi.tool.validation=true

# QPS验证基线测试配置
baseline.qps.ratio=0.5
baseline.duration.seconds=120

# QPS验证峰值测试配置
peak.qps.ratio=1.2
peak.duration.seconds=60
peak.success.rate.threshold=95.0

# QPS验证恢复测试配置
recovery.wait.seconds=30
recovery.qps.ratio=1.0
recovery.duration.seconds=120

# ==================== QPS验证专用工具配置 ====================

# QPS验证Gatling配置
gatling.simulation.class=com.yeepay.g3.yop.performance.gatling.QpsVerificationTest
gatling.results.dir=target/gatling-qps

# QPS验证JMeter配置
jmeter.test.plan=src/test/resources/YOP-Gateway-QPS-Validation.jmx
jmeter.results.dir=target/jmeter-qps

# ==================== QPS验证报告配置 ====================

# QPS验证报告生成设置
report.output.dir=target/qps-validation-reports

# QPS验证报告通知设置
report.email.enabled=false
report.slack.enabled=false

# ==================== QPS验证专用监控配置 ====================

# QPS验证指标收集
metrics.retention.hours=24

# QPS验证Prometheus集成
prometheus.enabled=true
prometheus.port=9090
prometheus.metrics.path=/metrics

# QPS验证InfluxDB集成
influxdb.enabled=false
influxdb.url=http://localhost:8086
influxdb.database=yop_qps_performance

# ==================== QPS验证测试API配置 ====================

# QPS验证测试API权重配置
test.apis.payment.enabled=true
test.apis.payment.weight=50
test.apis.auth.enabled=true
test.apis.auth.weight=30
test.apis.query.enabled=true
test.apis.query.weight=20

# ==================== QPS验证断路器配置 ====================

# QPS验证断路器配置
circuit.breaker.enabled=true
circuit.breaker.failure.threshold=50
circuit.breaker.timeout.seconds=60

# ==================== QPS验证专用调试配置 ====================

# QPS验证日志级别
log.level.performance=DEBUG
log.level.gatling=WARN
log.level.jmeter=WARN

# QPS验证性能分析
profiling.enabled=false
profiling.cpu.enabled=false
profiling.memory.enabled=false

# ==================== QPS验证规则配置 ====================

# QPS验证规则
qps.validation.tolerance.percent=5.0
qps.validation.min.samples=1000
qps.validation.stability.window.seconds=60

# QPS验证延迟规则
latency.validation.outlier.threshold=3.0
latency.validation.trend.analysis=true
latency.validation.percentile.accuracy=0.1

# QPS验证成功率规则
success.rate.validation.min.requests=100
success.rate.validation.sliding.window.seconds=30
success.rate.validation.error.categorization=true

# QPS验证稳定性规则
stability.validation.coefficient.variation.max=0.2
stability.validation.trend.slope.max=0.1
stability.validation.outlier.ratio.max=0.05