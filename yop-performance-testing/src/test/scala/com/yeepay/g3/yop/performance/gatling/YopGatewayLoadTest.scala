package com.yeepay.g3.yop.performance.gatling

import io.gatling.core.Predef._
import io.gatling.http.Predef._
import scala.concurrent.duration._
import scala.util.Random

/**
 * YOP网关Gatling高性能压测脚本
 * 
 * 测试目标：
 * - QPS 2000稳定性验证
 * - TP99 < 20ms延迟验证
 * - 系统极限承载能力测试
 * - 真实业务场景仿真
 * 
 * 测试场景：
 * 1. 支付API创建订单
 * 2. 认证API获取Token
 * 3. 查询API订单状态
 * 4. 混合业务场景
 * 
 * <AUTHOR>
 * @since 2025/1/1
 */
class YopGatewayLoadTest extends Simulation {

  // ==================== 配置参数 ====================
  
  val targetHost = System.getProperty("target.host", "localhost")
  val targetPort = System.getProperty("target.port", "8064").toInt
  val targetProtocol = System.getProperty("target.protocol", "http")
  val basePath = System.getProperty("base.path", "/yop-center")
  
  val targetQps = System.getProperty("target.qps", "2000").toInt
  val testDuration = System.getProperty("test.duration", "300").toInt.seconds
  val rampDuration = System.getProperty("ramp.duration", "120").toInt.seconds
  val maxUsers = System.getProperty("max.users", "500").toInt
  
  // ==================== HTTP协议配置 ====================
  
  val httpProtocol = http
    .baseUrl(s"$targetProtocol://$targetHost:$targetPort$basePath")
    .acceptHeader("application/json")
    .contentTypeHeader("application/json")
    .userAgentHeader("Gatling-YOP-LoadTest/1.0")
    .keepAliveHeader("keep-alive")
    .connectionHeader("keep-alive")
    .disableFollowRedirect
    .shareConnections // 重用连接以提高性能

  // ==================== 测试数据 ====================
  
  // 用户数据源
  val userDataFeeder = csv("testdata/users.csv").random
  
  // 动态生成订单数据
  def generateOrderData() = Map(
    "orderId" -> s"ORD-${System.currentTimeMillis()}-${Random.nextInt(9999)}",
    "amount" -> (Random.nextInt(9900) + 100), // 100-10000
    "timestamp" -> System.currentTimeMillis(),
    "nonce" -> Random.nextInt(999999)
  )

  // ==================== 测试场景定义 ====================
  
  /**
   * 支付API测试场景
   */
  val paymentScenario = scenario("Payment API Load Test")
    .feed(userDataFeeder)
    .exec { session =>
      val orderData = generateOrderData()
      session.setAll(orderData)
    }
    .exec(
      http("Create Payment Order")
        .post("/api/payment/create")
        .body(StringBody(session => s"""{
          "orderId": "${session("orderId").as[String]}",
          "amount": ${session("amount").as[Int]},
          "currency": "CNY",
          "userId": "${session("userId").as[String]}",
          "appId": "${session("appId").as[String]}",
          "notifyUrl": "https://example.com/notify",
          "returnUrl": "https://example.com/return",
          "orderInfo": {
            "productName": "测试商品${scala.util.Random.nextInt(100)}",
            "productDesc": "Gatling性能测试商品",
            "productType": "digital"
          },
          "timestamp": ${session("timestamp").as[Long]},
          "version": "1.0"
        }""")).asJson
        .check(
          status.is(200),
          jsonPath("$.success").is("true"),
          responseTimeInMillis.lt(50) // 期望响应时间 < 50ms
        )
    )
    .pause(100.milliseconds, 500.milliseconds) // 随机暂停模拟真实用户行为

  /**
   * 认证API测试场景
   */
  val authScenario = scenario("Authentication API Load Test")
    .feed(userDataFeeder)
    .exec { session =>
      val authData = generateOrderData()
      session.setAll(authData)
    }
    .exec(
      http("Get Authentication Token")
        .post("/api/auth/token")
        .body(StringBody(session => s"""{
          "appId": "${session("appId").as[String]}",
          "appKey": "${session("appKey").as[String]}",
          "timestamp": ${session("timestamp").as[Long]},
          "nonce": "${session("nonce").as[Int]}",
          "grantType": "client_credentials"
        }""")).asJson
        .check(
          status.is(200),
          jsonPath("$.success").is("true"),
          jsonPath("$.data.accessToken").exists,
          responseTimeInMillis.lt(30) // 认证应该更快
        )
    )
    .pause(50.milliseconds, 200.milliseconds)

  /**
   * 查询API测试场景
   */
  val queryScenario = scenario("Query API Load Test")
    .feed(userDataFeeder)
    .exec { session =>
      val queryData = generateOrderData()
      session.setAll(queryData)
    }
    .exec(
      http("Query Order Status")
        .get("/api/order/status")
        .queryParam("orderId", session => s"ORD-TEST-${session("nonce").as[Int]}")
        .queryParam("appId", "${appId}")
        .check(
          status.is(200),
          responseTimeInMillis.lt(20) // 查询应该最快
        )
    )
    .pause(200.milliseconds, 800.milliseconds)

  /**
   * 混合业务场景 - 模拟真实用户行为
   */
  val mixedBusinessScenario = scenario("Mixed Business Scenario")
    .feed(userDataFeeder)
    .exec { session =>
      val businessData = generateOrderData()
      session.setAll(businessData)
    }
    // 1. 首先获取认证token
    .exec(
      http("Auth - Get Token")
        .post("/api/auth/token")
        .body(StringBody(session => s"""{
          "appId": "${session("appId").as[String]}",
          "appKey": "${session("appKey").as[String]}",
          "timestamp": ${session("timestamp").as[Long]},
          "nonce": "${session("nonce").as[Int]}",
          "grantType": "client_credentials"
        }""")).asJson
        .check(
          status.is(200),
          jsonPath("$.data.accessToken").saveAs("accessToken")
        )
    )
    .pause(100.milliseconds, 300.milliseconds)
    
    // 2. 创建支付订单
    .exec(
      http("Payment - Create Order")
        .post("/api/payment/create")
        .header("Authorization", "Bearer ${accessToken}")
        .body(StringBody(session => s"""{
          "orderId": "${session("orderId").as[String]}",
          "amount": ${session("amount").as[Int]},
          "currency": "CNY",
          "userId": "${session("userId").as[String]}",
          "appId": "${session("appId").as[String]}",
          "timestamp": ${session("timestamp").as[Long]},
          "version": "1.0"
        }""")).asJson
        .check(
          status.is(200),
          jsonPath("$.success").is("true")
        )
    )
    .pause(500.milliseconds, 1.second)
    
    // 3. 查询订单状态
    .exec(
      http("Query - Order Status")
        .get("/api/order/status")
        .header("Authorization", "Bearer ${accessToken}")
        .queryParam("orderId", "${orderId}")
        .queryParam("appId", "${appId}")
        .check(
          status.is(200)
        )
    )
    .pause(1.second, 3.seconds)

  // ==================== 负载模式定义 ====================
  
  /**
   * QPS目标验证测试 - 2000 QPS稳定运行
   */
  val qpsTargetTest = paymentScenario
    .inject(
      constantRate(targetQps requestsPerSec) during testDuration // 使用constantRate精确控制QPS
    )
    .protocols(httpProtocol)

  /**
   * 渐进式压力测试 - 逐步增加到目标QPS
   */
  val progressiveLoadTest = paymentScenario
    .inject(
      rampRate(10 requestsPerSec) to (targetQps requestsPerSec) during rampDuration,
      constantRate(targetQps requestsPerSec) during testDuration
    )
    .protocols(httpProtocol)

  /**
   * 峰值压力测试 - 测试系统极限
   */
  val peakLoadTest = paymentScenario
    .inject(
      rampRate(10 requestsPerSec) to (targetQps * 2 requestsPerSec) during (rampDuration / 2), // 快速达到2倍QPS
      constantRate(targetQps * 2 requestsPerSec) during (testDuration / 2)
    )
    .protocols(httpProtocol)

  /**
   * 混合场景压力测试 - 模拟真实业务比例
   */
  val mixedLoadTest = List(
    paymentScenario.inject(
      rampRate(5 requestsPerSec) to ((targetQps * 0.5).toInt requestsPerSec) during rampDuration, // 50%支付
      constantRate((targetQps * 0.5).toInt requestsPerSec) during testDuration
    ),
    authScenario.inject(
      rampRate(3 requestsPerSec) to ((targetQps * 0.3).toInt requestsPerSec) during rampDuration,  // 30%认证
      constantRate((targetQps * 0.3).toInt requestsPerSec) during testDuration
    ),
    queryScenario.inject(
      rampRate(2 requestsPerSec) to ((targetQps * 0.2).toInt requestsPerSec) during rampDuration,  // 20%查询
      constantRate((targetQps * 0.2).toInt requestsPerSec) during testDuration
    )
  )

  /**
   * 完整业务流程测试 - 端到端场景
   */
  val fullBusinessTest = mixedBusinessScenario
    .inject(
      rampUsers(maxUsers / 4) during rampDuration,
      constantUsers(maxUsers / 4) during testDuration
    )
    .protocols(httpProtocol)

  // ==================== 断言配置 ====================
  
  /**
   * 性能目标断言
   */
  val performanceAssertions = Seq(
    // 全局响应时间断言
    global.responseTime.percentile3.lt(20), // TP99 < 20ms
    global.responseTime.percentile4.lt(50), // TP95 < 50ms
    global.responseTime.mean.lt(10),        // 平均响应时间 < 10ms
    
    // 成功率断言
    global.successfulRequests.percent.gt(99.5), // 成功率 > 99.5%
    
    // QPS断言
    global.requestsPerSec.gt(targetQps * 0.9) // 实际QPS >= 目标QPS的90%
  )

  // ==================== 测试执行配置 ====================
  
  /**
   * 根据系统属性选择测试模式
   */
  val testMode = System.getProperty("test.mode", "qps_target")
  
  val selectedTest = testMode match {
    case "qps_target" => 
      println(s"Running QPS Target Test: $targetQps QPS for $testDuration")
      qpsTargetTest
      
    case "progressive" => 
      println(s"Running Progressive Load Test: ramp to $targetQps QPS")
      progressiveLoadTest
      
    case "peak" => 
      println(s"Running Peak Load Test: up to ${targetQps * 2} QPS")
      peakLoadTest
      
    case "mixed" => 
      println("Running Mixed Business Scenarios")
      mixedLoadTest
      
    case "full_business" => 
      println("Running Full Business Flow Test")
      fullBusinessTest
      
    case _ => 
      println(s"Unknown test mode: $testMode, using QPS target test")
      qpsTargetTest
  }

  // ==================== 主测试配置 ====================
  
  setUp(
    testMode match {
      case "mixed" => mixedLoadTest
      case _ => selectedTest
    }
  ).protocols(httpProtocol)
   .assertions(performanceAssertions: _*)
   .maxDuration(testDuration + rampDuration + 60.seconds) // 添加缓冲时间

  // ==================== 测试完成后处理 ====================
  
  after {
    println("========== YOP Gateway Load Test Completed ==========")
    println(s"Test Mode: $testMode")
    println(s"Target: $targetProtocol://$targetHost:$targetPort$basePath")
    println(s"Duration: $testDuration, Ramp: $rampDuration")
    println(s"Target QPS: $targetQps, Max Users: $maxUsers")
    println("=".repeat(50))
  }
}

/**
 * 专门的QPS验证测试类
 */
class QpsVerificationTest extends Simulation {
  val httpProtocol = http
    .baseUrl(System.getProperty("target.url", "http://localhost:8064/yop-center"))
    .acceptHeader("application/json")
    .contentTypeHeader("application/json")

  val targetQps = System.getProperty("target.qps", "2000").toInt
  val testDuration = 30.minutes

  val qpsVerificationScenario = scenario("QPS 2000 Verification")
    .exec(
      http("QPS Test Request")
        .post("/api/payment/create")
        .body(StringBody("""{
          "orderId": "QPS-TEST-${__Random(100000,999999)}",
          "amount": 10000,
          "currency": "CNY",
          "userId": "qps-test-user",
          "appId": "qps-test-app"
        }""")).asJson
        .check(
          status.is(200),
          responseTimeInMillis.lt(20)
        )
    )

  setUp(
    qpsVerificationScenario.inject(
      constantRate(targetQps requestsPerSec) during testDuration
    )
  ).protocols(httpProtocol)
   .assertions(
     global.responseTime.percentile3.lt(20),
     global.successfulRequests.percent.gt(99.9),
     global.requestsPerSec.gt(targetQps * 0.95)
   )
}

/**
 * TP99延迟验证测试类
 */
class LatencyVerificationTest extends Simulation {
  val httpProtocol = http
    .baseUrl(System.getProperty("target.url", "http://localhost:8064/yop-center"))
    .acceptHeader("application/json")

  val latencyTestScenario = scenario("TP99 < 20ms Verification")
    .exec(
      http("Latency Test Request")
        .post("/api/payment/create")
        .body(StringBody("""{
          "orderId": "LATENCY-TEST-${__Random(100000,999999)}",
          "amount": 5000,
          "currency": "CNY"
        }""")).asJson
        .check(
          status.is(200),
          responseTimeInMillis.saveAs("responseTime")
        )
    )
    .exec { session =>
      val responseTime = session("responseTime").as[Int]
      if (responseTime > 50) {
        println(s"Warning: High response time detected: ${responseTime}ms")
      }
      session
    }

  setUp(
    latencyTestScenario.inject(
      constantUsersPerSec(50) during 10.minutes
    )
  ).protocols(httpProtocol)
   .assertions(
     global.responseTime.percentile3.lt(20), // TP99 < 20ms
     global.responseTime.percentile4.lt(15), // TP95 < 15ms
     global.responseTime.mean.lt(8)          // 平均 < 8ms
   )
}