<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yeepay.g3.yop</groupId>
    <artifactId>yop-performance-testing</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>YOP Gateway Performance Testing Suite</name>
    <description>YOP网关性能测试和压测综合测试套件</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- 核心框架版本 -->
        <jmh.version>1.37</jmh.version>
        <junit.version>5.10.0</junit.version>
        <testng.version>7.8.0</testng.version>
        <gatling.version>3.9.5</gatling.version>
        <spring.boot.version>2.7.15</spring.boot.version>
        
        <!-- Netty and HTTP 客户端 -->
        <netty.version>4.1.97.Final</netty.version>
        <okhttp.version>4.11.0</okhttp.version>
        <apache.httpclient.version>4.5.14</apache.httpclient.version>
        
        <!-- 监控和度量 -->
        <micrometer.version>1.9.15</micrometer.version>
        <prometheus.version>0.16.0</prometheus.version>
        <influxdb.version>2.23</influxdb.version>
        
        <!-- JSON处理 -->
        <jackson.version>2.15.2</jackson.version>
        <fastjson.version>2.0.40</fastjson.version>
        
        <!-- 工具库 -->
        <lombok.version>1.18.30</lombok.version>
        <guava.version>32.1.2-jre</guava.version>
        <commons.lang3.version>3.13.0</commons.lang3.version>
        
        <!-- 测试数据生成 -->
        <datafaker.version>2.0.2</datafaker.version>
        
        <!-- Docker测试 -->
        <testcontainers.version>1.19.1</testcontainers.version>
    </properties>

    <dependencies>
        <!-- =========================== JMH 微基准测试框架 =========================== -->
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-core</artifactId>
            <version>${jmh.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-generator-annprocess</artifactId>
            <version>${jmh.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- =========================== 测试框架 =========================== -->
        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
        </dependency>
        
        <!-- TestNG -->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>${testng.version}</version>
        </dependency>

        <!-- =========================== Gatling 高性能压测 =========================== -->
        <dependency>
            <groupId>io.gatling.highcharts</groupId>
            <artifactId>gatling-charts-highcharts</artifactId>
            <version>${gatling.version}</version>
        </dependency>
        <dependency>
            <groupId>io.gatling</groupId>
            <artifactId>gatling-http-java</artifactId>
            <version>${gatling.version}</version>
        </dependency>

        <!-- =========================== HTTP 客户端 =========================== -->
        <!-- Netty 异步HTTP客户端 -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty.version}</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
            <version>${netty.version}</version>
        </dependency>
        
        <!-- OkHttp 高性能HTTP客户端 -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>
        
        <!-- Apache HttpClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${apache.httpclient.version}</version>
        </dependency>

        <!-- =========================== 监控和度量 =========================== -->
        <!-- Micrometer 核心 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <version>${micrometer.version}</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>${micrometer.version}</version>
        </dependency>
        
        <!-- Prometheus客户端 -->
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient</artifactId>
            <version>${prometheus.version}</version>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_hotspot</artifactId>
            <version>${prometheus.version}</version>
        </dependency>
        
        <!-- InfluxDB 时序数据库客户端 -->
        <dependency>
            <groupId>org.influxdb</groupId>
            <artifactId>influxdb-java</artifactId>
            <version>${influxdb.version}</version>
        </dependency>

        <!-- =========================== JSON 处理 =========================== -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <!-- =========================== 工具库 =========================== -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons.lang3.version}</version>
        </dependency>

        <!-- =========================== 测试数据生成 =========================== -->
        <dependency>
            <groupId>net.datafaker</groupId>
            <artifactId>datafaker</artifactId>
            <version>${datafaker.version}</version>
        </dependency>

        <!-- =========================== Docker 测试环境 =========================== -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <version>${testcontainers.version}</version>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${testcontainers.version}</version>
        </dependency>

        <!-- =========================== Spring Boot (可选，用于配置管理) =========================== -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>${spring.boot.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${spring.boot.version}</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- JMH 基准测试打包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>jmh-benchmarks</id>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>yop-benchmarks</finalName>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>org.openjdk.jmh.Main</mainClass>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Gatling 压测插件 -->
            <plugin>
                <groupId>io.gatling</groupId>
                <artifactId>gatling-maven-plugin</artifactId>
                <version>${gatling.version}</version>
                <configuration>
                    <!-- 移除硬编码的simulationClass，支持通过命令行参数指定 -->
                    <!-- 使用方式: mvn gatling:test -Dgatling.simulationClass=com.yeepay.g3.yop.performance.gatling.YopGatewayLoadTest -->
                    <runMultipleSimulations>false</runMultipleSimulations>
                    <includes>
                        <include>**/*Test.scala</include>
                        <include>**/*Simulation.scala</include>
                    </includes>
                    <excludes>
                        <exclude>**/*BaseTest.scala</exclude>
                    </excludes>
                    <!-- 结果目录配置 -->
                    <resultsFolder>target/gatling</resultsFolder>
                    <!-- JVM参数配置 -->
                    <jvmArgs>
                        <jvmArg>-Xms2g</jvmArg>
                        <jvmArg>-Xmx4g</jvmArg>
                        <jvmArg>-XX:+UseG1GC</jvmArg>
                        <jvmArg>-XX:MaxGCPauseMillis=100</jvmArg>
                        <jvmArg>-Dfile.encoding=UTF-8</jvmArg>
                    </jvmArgs>
                </configuration>
            </plugin>

            <!-- Surefire 单元测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <parallel>methods</parallel>
                    <threadCount>4</threadCount>
                </configuration>
            </plugin>

            <!-- Failsafe 集成测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.1.2</version>
            </plugin>

            <!-- Docker 镜像构建插件 -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <repository>yop-performance-testing</repository>
                    <tag>latest</tag>
                </configuration>
            </plugin>

            <!-- 性能测试报告插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>com.yeepay.g3.yop.performance.runner.PerformanceTestRunner</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 构建配置文件 - 支持不同的测试环境 -->
    <profiles>
        <!-- 本地开发环境 -->
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <test.target.host>localhost</test.target.host>
                <test.target.port>8064</test.target.port>
                <test.duration>60</test.duration>
                <test.max.qps>1000</test.max.qps>
            </properties>
        </profile>

        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <test.target.host>yop-gateway-test</test.target.host>
                <test.target.port>8080</test.target.port>
                <test.duration>300</test.duration>
                <test.max.qps>2000</test.max.qps>
            </properties>
        </profile>

        <!-- 生产性能测试环境 -->
        <profile>
            <id>performance</id>
            <properties>
                <test.target.host>yop-gateway-perf</test.target.host>
                <test.target.port>8080</test.target.port>
                <test.duration>1800</test.duration>
                <test.max.qps>3000</test.max.qps>
            </properties>
        </profile>
    </profiles>
</project>