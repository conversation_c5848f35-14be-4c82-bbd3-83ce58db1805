# TP99 < 20ms延迟验证测试配置文件
# 用于配置延迟性能验证的各项参数
# 继承基础配置：performance.properties

# ===== 延迟验证特有配置 =====
# 延迟验证测试特定的目标服务配置（如果与基础配置不同）
latency.validation.host=localhost
latency.validation.port=8080
latency.validation.basePath=/yop/api

# ===== 延迟验证专用测试配置 =====
# 基线延迟测试配置
latency.baseline.qps=100
latency.baseline.duration=120
latency.baseline.users=50
latency.baseline.tp99Threshold=10

# 负载延迟测试配置
latency.load.qps=2000
latency.load.duration=300
latency.load.rampDuration=120
latency.load.maxUsers=1000
latency.load.minQpsRequirement=1900

# 稳定性测试配置
latency.stability.qps=2000
latency.stability.duration=600
latency.stability.rampDuration=120
latency.stability.maxUsers=1000

# 分布测试配置
latency.distribution.qps=1500
latency.distribution.duration=300
latency.distribution.users=750

# 回归测试配置
latency.regression.qps=2000
latency.regression.duration=240
latency.regression.users=1000
latency.regression.recoveryWait=30
latency.regression.threshold=1.1
latency.regression.qpsThreshold=0.95

# ===== 延迟验证特有配置 =====
# Gatling延迟验证模拟器配置
gatling.simulation.packageName=com.yeepay.g3.yop.performance.gatling

# 延迟验证专用测试数据
test.data.requestTemplatesFile=src/test/resources/test-data/latency-request-templates.json

# ===== 延迟验证专用配置 =====
# 延迟验证结果目录
results.directory=target/latency-validation-results

# 延迟验证报告模板
results.reportTemplate=src/test/resources/templates/latency-validation-report.html

# 延迟验证自动打开报告
results.autoOpenReport=true

# 延迟验证专用告警阈值
alert.latency.threshold=25

# 延迟验证专用日志文件
logging.file=target/logs/latency-validation.log

# 延迟验证专用调试日志目录
debug.logDirectory=target/debug-logs/latency-validation